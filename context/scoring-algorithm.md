# Kundli Compatibility Scoring Algorithm (Completion Model)

## Kundli Vibe - AI-Powered Scoring Algorithm

## Overview
This document outlines the AI-driven compatibility scoring system that powers Ku<PERSON>li Vibe. Our approach combines traditional Vedic astrology with artificial intelligence to provide intelligent, personalized, and deeply insightful compatibility analysis that speaks to both modern users and traditional families.

## Core Philosophy
**AI translates traditional astrological data into practical, life-aligned insights while maintaining authenticity and cultural respect.**

Our AI layer converts complex planetary positions and traditional scores into natural-language explanations, interactive scenarios, and personalized recommendations that bridge the gap between ancient wisdom and modern relationship needs.

## AI Integration Strategy

### 1. AI as the Intelligence Layer
- **Smart Interpretation**: AI analyzes traditional scores and converts them into contextual, practical insights
- **Natural Language Generation**: AI creates conversational explanations for every compatibility factor
- **Personalized Narratives**: AI generates unique relationship stories based on planetary interactions
- **Adaptive Explanations**: AI adjusts complexity and tone based on user profile and audience

### 2. AI-Enhanced Scoring Process
- **Traditional Foundation**: Authentic Vedic calculations (Guna Milan, dosha analysis, dasha periods)
- **AI Amplification**: Intelligent interpretation of traditional scores in modern context
- **Completion-Based Analysis**: AI identifies how charts balance and complete each other
- **Scenario Modeling**: AI simulates relationship dynamics across different life phases

The algorithm goes beyond traditional Guna Milan to provide a holistic, completion-based compatibility model that evaluates how two charts balance and complement each other across multiple life aspects. The outcome is:

- **Overall Compatibility** (0–100)
- **Aspect Scores** (Emotional, Health, Finance, Career, Family, Children, Sexual, Spiritual, Timing, Social/Familial)
- **Confidence Score** (data quality + astrological signal strength)
- **Explainable Insights** (plain-language narratives tied to each score)

## Dual Presentation Strategy

The algorithm must present results to bridge generational perspectives:

### For Traditional Families:
- **Guna Milan Score**: Traditional 36-point system with explanations
- **Dosha Analysis**: Manglik, Nadi, and other traditional considerations
- **Planetary Positions**: Familiar astrological terminology and concepts
- **Cultural Context**: References to traditional wisdom and practices

### For Modern Users:
- **Life Compatibility Score**: 100-point system across practical life aspects
- **Data-Driven Insights**: Evidence-based explanations with logical reasoning
- **Practical Applications**: How compatibility affects daily life, goals, and relationships
- **Visual Analytics**: Charts, graphs, and modern data presentation

## 0. Purpose & Philosophy

This document defines a complete, implementation-ready scoring system for kundli compatibility that goes beyond Guna Milan while respecting traditional methods. It measures how two charts complete (balance/offset) one another across key life domains. The outcome is:

- **Overall Compatibility** (0–100)
- **Aspect Scores** (Emotional, Health, Finance, Career, Family, Children, Sexual, Spiritual, Timing, Social/Familial)
- **Confidence Score** (data quality + astrological signal strength)
- **Explainable Insights** (plain-language narratives tied to each score)

**Principle**: Reward balance (A strong where B is weak) and alignment (both suitably strong where needed), and penalize double-weak & hard conflicts that remain unmitigated.

## 1. Inputs & Assumptions

### 1.1 Required Inputs (both individuals)

- Full name (for report only)
- DOB (YYYY-MM-DD), TOB (24h), POB (lat, lon, timezone)
- Gender (for some classical rules)
- Optional: user-weighted priorities (e.g., "Career 1.5×")

### 1.2 Charts & Data Generated

- **Rāśi (D1)**: sign, degree, house placement of planets (Sun, Moon, Mars, Mercury, Jupiter, Venus, Saturn, Rahu, Ketu)
- **Navāṁśa (D9)**: divisional placements (marital/spousal quality)
- **House Strengths**: functional benefic/malefic by lagna; dignity by sign & navāṁśa; shadbala-like composite if implemented
- **Aspects**: graha dṛṣṭi + rashi aspects if used
- **Yogas/Doshas**: Manglik, Nādi, Rāśi–Gana, etc. (for baseline parity)
- **Dasha Windows**: Current & next ~15–20 years (e.g., Vimshottari)

### 1.3 Calculation Standards

- **Ayanāṁśa**: Lahiri (default)
- **House System**: Whole Sign (default; Placidus optional)

**Aspect Orbs (defaults)**:

- Conjunction: ≤ 8°
- Opposition: ≤ 6°
- Trine/Sextile: ≤ 6°/4°
- Square/Quincunx (if used): ≤ 5°/3°

**Strength mapping (dignity tiers → numeric)**:

- Exalted +2.0, Own +1.5, Mooltrikona +1.2, Friendly +0.8, Neutral +0.5, Enemy +0.2, Debilitated 0.0
- (Scale later normalized to 0–1.)

## 2. Scoring Overview

### 2.1 Aspect Buckets & Default Weights (sum = 100)

- **Emotional & Mental** – 14
- **Health & Longevity** – 10
- **Finance & Wealth** – 12
- **Career & Stability** – 12
- **Family & Domestic Harmony** – 10
- **Children/Progeny Potential** – 8
- **Sexual & Intimacy** – 8
- **Spiritual/Values Alignment** – 8
- **Timing Harmony (Dashas)** – 10
- **Social/Familial Fit** – 8

Users can re-weight (e.g., “Career ×1.5”), after which weights are re-normalized to 100.

### 2.2 Score Construction (per aspect)

For each aspect A:

```
A_score = clamp01( BaseCompatibility(A) + ComplementarityBonus(A) - ConflictPenalty(A) ) × 100
Weighted_A = A_score × (Weight_A / 100)
Overall = Σ Weighted_A (rounded to 0–100)
```

## 3. Core Constructs

### 3.1 Compatibility vs. Complementarity

- **Compatibility**: Similarity/beneficial resonance (e.g., harmonious Moon–Moon, Venus–Mars trines, aligned 7th lords)
- **Complementarity**: One partner's strong factor offsets the other's weakness (e.g., Partner A weak 10th house, Partner B strong 10th/11th/Jupiter)

We measure both; the model rewards complementarity almost as much as direct compatibility.

### 3.2 Balance Function (for pairs of signals)

Given scores x (Partner A) and y (Partner B) for a factor:

- **Resonance (both strong)**: `Res = (x + y)/2`
- **Complementarity (one strong, one weak)**:
  ```
  Comp = 1 - |x - y| (high when they're different)
  if min(x, y) < 0.25: Comp *= 0.6
  if min(x, y) < 0.10: Comp *= 0.3
  ```
- **Harmonic Mean for "both-needed" factors**: `HM = 2xy/(x+y+ε)` (punishes double-weak)

## 4. Aspect-by-Aspect Specification

Below, each aspect lists Signals, Scoring Steps, and Penalties/Bonuses. All intermediate signals are mapped to 0–1 before combination.

### 4.1 Emotional & Mental (14)

Signals

- Moon–Moon relationship (sign element/modality, aspects, house overlays)
- Moon dignity/strength in D1 & D9
- Mercury (communication) dignity and Mercury–Moon relation
- 5th house (affection/romance), 4th (contentment)
- Afflictions to Moon (Saturn, Rahu/Ketu, Mars) and mitigating benefics (Jupiter/Venus)

Scoring

```
MoonPair = 0.6*Res(Moon_strengths) + 0.4*CompatMap(Moon_signs/aspects)
Comm = Res(Mercury_strengths) + Compat(Mercury aspects) (normalize)
Affect = HM(5th_house_scores)
Stability = 1 - SevereAfflictionIndex(Moon) (with Jupiter/Venus relief)
Base = 0.35*MoonPair + 0.25*Comm + 0.2*Affect + 0.2*Stability
```

Penalty/Bonus

- Penalty for both Moons severely afflicted (≥2 malefic hits without benefic aspects): −0.15
- Bonus if Moon–Moon trine/sextile and at least one exalted/own: +0.1

### 4.2 Health & Longevity (10)

**Signals**

- 6th/8th/12th houses strength & affliction
- Mars/Saturn placements and aspects to lagna & Moon
- D9 support for physical vitality
- Manglik interaction (see §5)

**Scoring**

```
Vitality = HM(lagna_strength, Sun_strength, Mars_strength)
Resilience = 1 - AfflictionIndex(6th,8th,12th)
Base = 0.5*Vitality + 0.5*Resilience
```

**Penalty/Bonus**

- Double-weak (both have heavily afflicted 8th with no benefic relief): −0.15
- One partner strong Resilience and other weak: +0.08 (complementarity)

### 4.3 Finance & Wealth (12)

**Signals**

- 2nd/11th houses, 2nd/11th lords dignity
- Jupiter & Venus strengths; 10th as feeder
- Wealth yogas (compressed to a 0–1 index)

**Scoring**

```
Earnings = Res(10th+2nd composite)
Gains = Res(11th composite)
Prosperity = 0.6*Earnings + 0.4*Gains
Complement = Complementarity(Earnings_A, Earnings_B)
Base = 0.8*Prosperity + 0.2*Complement
```

**Penalty/Bonus**

- Both weak 2nd & 11th (<0.3): −0.12
- Strong Jupiter in at least one + positive aspect to 2nd/11th: +0.07

### 4.4 Career & Stability (12)

**Signals**

- 10th house/10th lord dignity & aspects
- Saturn (structure) + Sun (authority) strength
- Dasha support for career years ahead

**Scoring**

```
Structure = Res(Saturn_strengths)
Authority = Res(Sun_strengths)
CareerCore = 0.5*Res(10th composite) + 0.25*Structure + 0.25*Authority
TimingAdj = Avg(UpcomingCareerDashaSupport_5yrs) (0–1)
Base = 0.8*CareerCore + 0.2*TimingAdj
```

**Penalty/Bonus**

- Both weak 10th and Saturn (<0.3): −0.12
- One strong Saturn or 10th offsets the other's weakness: +0.08

### 4.5 Family & Domestic Harmony (10)

**Signals**

- 4th house (home peace), Venus/Moon harmony, 2nd (family cohesion)
- Benefic presence in 4th; malefic affliction index

**Scoring**

```
Home = HM(4th_house_scores A & B)
Affection = Res(Venus+Moon strengths)
Base = 0.6*Home + 0.4*Affection
```

**Penalty/Bonus**

- Severe 4th affliction in both: −0.12
- Jupiter aspect to 4th in at least one: +0.05

### 4.6 Children/Progeny Potential (8)

**Signals**

- 5th house strength & afflictions
- Jupiter (general significator), additional female/male classical rules (optional)
- Dasha support for progeny windows (first 10 years)

**Scoring**

```
FifthCore = Res(5th_house_scores)
GuruHelp = Res(Jupiter_strengths)
Timing = Avg(ProgenyDashaSupport_10yrs)
Base = 0.5*FifthCore + 0.3*GuruHelp + 0.2*Timing
```

**Penalty/Bonus**

- Both 5th heavily afflicted: −0.15
- Complementarity (one strong 5th & Jupiter): +0.1

### 4.7 Sexual & Intimacy (8)

**Signals**

- 7th & 8th house conditions; Venus–Mars aspects
- 7th lord dignity; D9 support
- Afflictions to Venus/Mars

**Scoring**

```
Attraction = Compat(Venus–Mars aspects/signs)
Bond = HM(7th_house_composites)
Depth = Res(8th_house_scores)
Base = 0.4*Attraction + 0.35*Bond + 0.25*Depth
```

**Penalty/Bonus**

- Venus & Mars both afflicted in both charts: −0.12
- Venus–Mars trine/sextile (cross-chart) present: +0.1

### 4.8 Spiritual/Values Alignment (8)

**Signals**

- 9th house & lord; Jupiter; Ketu (higher orientation)
- D9 dharmic indicators; mutual sign affinities (fire/air etc.)

**Scoring**

```
Values = Res(9th_house)
Wisdom = Res(Jupiter_strengths)
Transcend = Ketu_support (capped)
Base = 0.5*Values + 0.35*Wisdom + 0.15*Transcend
```

**Penalty/Bonus**

- Both 9th weak (<0.3): −0.1
- Shared strong Jupiter or 9th: +0.07

### 4.9 Timing Harmony (Dashas) (10)

**Signals**

- Next 10–15 years Vimshottari windows for each
- Label windows for Supportive, Neutral, Stress per life area

**Scoring**

```
Build Annual Vector for each partner: Career, Health, Family, Finance tags per year (−1..+1)
Compute Overlap Index (OI) = mean over years of sign-aligned tags per area
Base = Avg(OI_Career, OI_Family, OI_Finance, OI_Health) (map to 0–1)
```

**Penalty/Bonus**

- Repeated anti-phase (one's peak vs other's crisis) ≥ 4 of 10 years: −0.12
- 3+ years of shared supportive windows in the first 7 years: +0.08

### 4.10 Social/Familial Fit (8)

**Signals**

- 2nd (family), 4th (home), 11th (community), 9th (traditions)
- Traditional kootas compressed to micro-signals: Nādi, Gana, Yoni (as soft modifiers, not hard gates)

**Scoring**

```
Cohesion = Res(2nd+4th+11th composites)
Trad = Res(9th)
Base = 0.7*Cohesion + 0.3*Trad
```

**Penalty/Bonus**

- Hard Nādi mismatch only if accompanied by double-weak 2nd/4th: −0.08
- Strong 11th or benefic to 2nd in either: +0.05

## 5. Dosha Handling (Softened, Contextual)

### 5.1 Manglik (Mangal Dosha)

- **Detection**: Mars in 1, 2, 4, 7, 8, 12 from lagna or Moon (choose stricter of two)
- **Cancellation** (any → reduce severity): Mars exalted/own/mooltrikona, strong benefic aspects on 7th/lagna, both partners Manglik (mutual)
- **Impact**: Contributes to Health, Sexual, Family penalties only if unmitigated and corroborated by afflicted 7th/8th/lagna
  - Mild: −0.03 each affected aspect
  - Moderate: −0.06
  - Severe: −0.10

### 5.2 Nādi, Gana, Yoni, Bhakoot

- Treated as micro-signals within Social/Familial Fit and Emotional
- Only trigger measurable penalties when corroborated by house/planet weaknesses (avoid superstition-driven hard fails)

## 6. Normalization & Indices

### 6.1 Planet/House Strength → [0,1]

Map dignity tiers to base numeric (see §1.3), average with:

- House support (benefic aspects +, malefic −)
- Divisional (D9) reinforcement (+ up to 0.2)
- Cap 0–1, then z-score vs. population baseline (optional) and squash with logistic for stability

### 6.2 Affliction Index (AI)

For a house/planet:

```
AI = 1 - clamp01( BaseStrength - 0.15*(#malefic_aspects weighted by orb) + 0.12*(#benefic_aspects weighted) )
```

Lower AI is better; convert to support = 1 - AI.

### 6.3 Yoga Compression

If you detect classic yogas, add small boosts to relevant composites (0.02–0.08), never exceeding caps.

## 7. Conflict, Complement, and Caps

### 7.1 ConflictPenalty(A)

Built from double-weak detectors and specific hard-conflict patterns (e.g., Venus & Mars both under malefics in both charts).

Clamp each aspect penalty to ≤ −0.15.

### 7.2 ComplementarityBonus(A)

If min(x,y) < 0.4 and max(x,y) > 0.7 in a key factor, add +0.05 to +0.10 depending on the aspect importance, capped so total aspect ≤ 1.0 before ×100.

## 8. Explainability Mapping

Each numeric outcome must attach human-readable reasons:

**Rules → Messages**

- e.g., "Moon–Moon trine and strong Mercury in both charts enhance emotional understanding."
- e.g., "Both 5th houses show strain; consider medical counsel and mindful planning."

**For every aspect A:**

- Show Top 3 Drivers (±) sorted by absolute contribution
- One Actionable Tip (practical, respectful)

## 9. Confidence Score (0–100)

```
Confidence = 100 × ( DataQuality × SignalStrength )
```

- **DataQuality**: TOB precision (exact ±5 min = 1.0; estimated hour = 0.7; unknown = 0.4), POB accuracy, ephemeris status
- **SignalStrength**: average absolute (|boosts| + |penalties|) density; wider margins → lower strength
- **Display**: confidence band (Low/Med/High). If Low, auto-insert disclaimer about time rectification

## 10. Thresholds & Labels (non-binary, gentle)

- **85–100**: Excellent Harmony
- **70–84**: Strong & Supportive
- **55–69**: Workable with Awareness
- **40–54**: Challenging; conscious effort needed
- **<40**: High Friction (seek guidance)

Always pair with nuanced text and improvement tips.

## 11. Personalization (User Weights)

If user sets priorities (e.g., Career 1.5×, Family 0.8×):

1. Multiply default weights
2. Re-normalize to sum to 100
3. Recompute Overall

## 12. Data Structures (for Engineering)

### 12.1 Per-Partner Feature Object (simplified)

```json
{
  "meta": { "dob": "", "tob": "", "pob": {"lat":0,"lon":0}, "gender": "" },
  "d1": { "lagna": "", "planets": { "moon": {"sign":"","deg":0,"house":1,...}, ... } },
  "d9": { ... },
  "strengths": { "houses": {"1":0.72,"2":0.61,...}, "planets": {"moon":0.83,...} },
  "aspects": [ {"from":"jupiter","to":"moon","type":"trine","orb":3.2}, ... ],
  "yogas": {"wealth_index":0.21, "vipareeta_index":0.08, ...},
  "afflictions": {"moon":0.18, "4th":0.25, ...},
  "dashas": [ {"start":"2026-03-01","end":"2031-05-15","area_tags":{"career":1,"family":0}}, ... ]
}
```

### 12.2 Pairwise Result Object

```json
{
  "aspect_scores": {
    "emotional": {"score":78, "drivers":[...], "penalties":[...]},
    "health": {...}, ...
  },
  "overall": 76,
  "confidence": 82,
  "weights": {...},
  "explanations": {"summary":"...", "tips":[ "...", "..." ]}
}
```

## 13. Pseudocode (Deterministic Core)

```python
function computeAspectScore(partnerA, partnerB, aspect):
    featsA, featsB = extractFeatures(aspect, partnerA, partnerB)
    comp = baseCompatibility(featsA, featsB)  // resonance
    comp = normalize01(comp)

    complement = complementarity(featsA, featsB)      // offsets
    penalty = conflictPenalty(featsA, featsB)         // double-weak etc.

    raw = clamp01(comp + complement - penalty)
    return round(raw * 100)

function computeOverall(A, B, weights):
    aspectScores = {}
    for aspect in ASPECT_LIST:
        aspectScores[aspect] = computeAspectScore(A, B, aspect)

    overall = sum( aspectScores[a] * weights[a] / 100 for a in ASPECT_LIST )
    conf = computeConfidence(A,B)
    return overall, aspectScores, conf
```

## 14. Validation & Calibration

Back-test on anonymized historical couple datasets (where outcomes are known), minimizing false certainty.

**Calibrate weightings and thresholds using:**

- Expert panels (astrologers) for qualitative alignment
- Bayesian or isotonic calibration so predicted bins match observed satisfaction outcomes
- Stress tests with edge cases (unknown TOB, twins, daylight time quirks)

**Fairness & Ethics**: Never render a binary "reject/accept." Always provide context, choice, and respect.

## 15. Fallbacks & Missing Data

**Unknown TOB:**

- Use Moon chart as fallback; suppress D9 reliance; lower Confidence
- Avoid precise house-based penalties; switch to sign-only compatibilities

**Location Ambiguity:** prompt correction; otherwise inflate time-zone ± errors and reduce Confidence

**Sparse Dasha:** compute Timing Harmony over shorter horizon; down-weight that aspect

## 16. Output Blueprint (User-Facing)

- Overall 0–100 + Label
- Radar/Bar chart of aspect scores
- Two to three-line summary
- Per-aspect cards: score, top drivers, one tip
- Confidence badge with reason (e.g., "Estimated birth time")
- Traditional koota sidebar (read-only, soft impact)

## 17. Examples (Illustrative)

**Strong Complementarity Case:**

- A weak 10th, B strong 10th+Saturn → Career 78 with +Complement bonus

**Double-Weak Warning:**

- Both 5th afflicted, no Jupiter relief → Children 42 with clear advisory

**Timing Anti-Phase:**

- A in Saturn–Mars stress; B in Jupiter–Venus peak → Timing Harmony 48 with −anti-phase penalty, and planning tip

## 18. Implementation Notes

- Keep all component scores traceable for auditability
- Centralize constants (weights, orbs, caps) in config for quick tuning
- Add unit tests per aspect with synthetic charts
- Ensure i18n for explanations

## TL;DR

A balanced, explainable, configurable framework that scores resonance + complementarity, softens traditional doshas via context, and produces actionable, humane guidance—never fatalistic. This is ready to translate into code with clear signals, formulas, and caps.
