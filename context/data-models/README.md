# Data Models

This folder contains data models, schemas, and database structures for the Kundli Vibe application.

## Structure
- `user-model.md` - User profile and authentication data
- `kundli-model.md` - Kundli/birth chart data structures
- `astrology-data.md` - Planetary positions, houses, aspects
- `predictions-model.md` - Prediction and reading data
- `database-schema.md` - Complete database schema design

## Data Model Template
```typescript
interface ModelName {
  id: string;
  // Add properties with types
  createdAt: Date;
  updatedAt: Date;
}
```

## Validation Rules
[Define data validation requirements]

## Relationships
[Document relationships between different data models]

## Data Sources
[List external data sources for astrological calculations]

## Privacy & Security
[Define data privacy and security requirements]
