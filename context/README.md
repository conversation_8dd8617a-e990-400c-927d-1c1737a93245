# Context Documentation

This folder contains all documentation and context files for the Kundli Vibe project to help with future development and AI assistance.

## Purpose
- Store project requirements, specifications, and design decisions
- Maintain context for AI assistants to understand project goals and constraints
- Document API specifications, data models, and architectural decisions
- Keep track of user preferences and project evolution

## Suggested Structure

```
context/
├── README.md                 # This file
├── project-overview.md       # High-level project description and goals
├── technical-specs.md        # Technical requirements and constraints
├── design-decisions.md       # Architecture and design choices made
├── api-specs/               # API documentation and specifications
├── user-stories/            # User requirements and stories
├── data-models/             # Database schemas and data structures
└── references/              # External resources and inspiration
```

## How to Use
1. Add new documentation files as the project evolves
2. Update existing files when requirements or decisions change
3. Reference these files when working with AI assistants for consistent context
4. Keep files organized and well-structured for easy reference

## Current Project Status
- **Framework**: React TypeScript with Create React App
- **UI Library**: Chakra UI v3
- **Theme**: Astrology/Kundli application with cosmic design
- **Status**: Basic landing page completed, ready for feature development
