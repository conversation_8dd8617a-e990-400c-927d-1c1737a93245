# User Stories

This folder contains user stories and requirements for the Kundli Vibe application.

## Structure
- `core-features.md` - Main application features and user flows
- `user-personas.md` - Target user profiles and characteristics
- `acceptance-criteria.md` - Detailed acceptance criteria for features
- `user-journey.md` - Complete user journey mapping
- `feedback-requirements.md` - User feedback and iteration requirements

## User Story Template
```
## [Feature Name]

**As a** [type of user]
**I want** [goal/desire]
**So that** [benefit/value]

### Acceptance Criteria
- [ ] [Specific requirement 1]
- [ ] [Specific requirement 2]
- [ ] [Specific requirement 3]

### Priority: [High/Medium/Low]
### Effort: [Small/Medium/Large]
### Dependencies: [List any dependencies]
```

## Priority Levels
- **High**: Core functionality, must-have features
- **Medium**: Important features that enhance user experience
- **Low**: Nice-to-have features for future iterations
