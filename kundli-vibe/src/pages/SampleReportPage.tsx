import React, { useState } from "react";
import {
  BrainCircuit,
  Heart,
  Star,
  Users,
  TrendingUp,
  MessageCircle,
  Download,
  Share2,
  FileText,
} from "lucide-react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

// Helper function to convert Tailwind color classes to hex colors
const getColorFromTailwind = (
  colorString: string,
  position: "start" | "end"
): string => {
  const colorMap: { [key: string]: string } = {
    "blue-400": "#60a5fa",
    "teal-300": "#5eead4",
    "amber-400": "#fbbf24",
    "yellow-300": "#fde047",
    "red-500": "#ef4444",
    "orange-400": "#fb923c",
    "pink-400": "#f472b6",
    "rose-400": "#fb7185",
    "purple-400": "#c084fc",
    "indigo-400": "#818cf8",
    "indigo-500": "#6366f1",
    "purple-500": "#a855f7",
  };

  const parts = colorString.split(" ");
  if (parts.length < 4) return "#6366f1"; // fallback color

  const targetPart = position === "start" ? parts[1] : parts[3];
  if (!targetPart) return "#6366f1";

  const cleanColor = targetPart.replace("from-", "").replace("to-", "");
  return colorMap[cleanColor] || "#6366f1";
};

const SampleReportPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("ai-insights");
  const [chatOpen, setChatOpen] = useState(false);

  return (
    <div className="min-h-screen w-full relative overflow-hidden">
      {/* Animated Background Blobs - matching landing page */}
      <div className="absolute inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        <div className="absolute top-1/2 right-1/3 w-60 h-60 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-60 animate-blob animation-delay-1000"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 w-full">
        {/* Hero Section */}
        <div className="w-full px-6 py-16 lg:py-24">
          <div className="max-w-6xl mx-auto text-center">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-6">
              <BrainCircuit className="w-4 h-4 mr-2 text-white/90" />
              <span className="text-white/90 text-sm font-medium">
                AI-Powered Vibe Match Report
              </span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-800 dark:text-white mb-6">
              Your Perfect
              <span className="block bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Compatibility Story
              </span>
            </h1>

            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-12">
              Discover how Priya & Rahul's stars align with modern AI insights
              and ancient Vedic wisdom
            </p>

            {/* Couple Cards */}
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16">
              {/* Bride Card */}
              <div className="group relative">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-pink-400 to-rose-500 rounded-3xl blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
                <div className="relative backdrop-blur-xl bg-white/10 rounded-3xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300">
                  <div className="text-center">
                    <div className="relative mb-6">
                      <div className="w-24 h-24 bg-gradient-to-br from-pink-400 via-rose-500 to-pink-600 rounded-full mx-auto flex items-center justify-center shadow-2xl">
                        <span className="text-2xl font-bold text-white">P</span>
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                        <span className="text-lg">♈</span>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-3">
                      Priya Sharma
                    </h3>
                    <div className="space-y-2 text-gray-600 dark:text-gray-300 text-sm">
                      <p>Born: March 15, 1995 • 10:30 AM</p>
                      <p>Mumbai, India</p>
                      <div className="inline-flex items-center px-3 py-1 bg-pink-500/20 rounded-full border border-pink-400/30 mt-3">
                        <span className="text-pink-600 dark:text-pink-300 text-xs font-medium">
                          Aries Sun • Cancer Moon
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Groom Card */}
              <div className="group relative">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-3xl blur opacity-30 group-hover:opacity-50 transition duration-300"></div>
                <div className="relative backdrop-blur-xl bg-white/10 rounded-3xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300">
                  <div className="text-center">
                    <div className="relative mb-6">
                      <div className="w-24 h-24 bg-gradient-to-br from-blue-400 via-indigo-500 to-blue-600 rounded-full mx-auto flex items-center justify-center shadow-2xl">
                        <span className="text-2xl font-bold text-white">R</span>
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                        <span className="text-lg">♋</span>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-3">
                      Rahul Gupta
                    </h3>
                    <div className="space-y-2 text-gray-600 dark:text-gray-300 text-sm">
                      <p>Born: July 22, 1993 • 2:15 PM</p>
                      <p>Delhi, India</p>
                      <div className="inline-flex items-center px-3 py-1 bg-blue-500/20 rounded-full border border-blue-400/30 mt-3">
                        <span className="text-blue-600 dark:text-blue-300 text-xs font-medium">
                          Cancer Sun • Virgo Moon
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Compatibility Score */}
            <div className="relative mb-16">
              <div className="absolute -inset-4 bg-gradient-to-r from-purple-400/20 via-pink-400/20 to-blue-400/20 rounded-3xl blur-2xl"></div>
              <div className="relative backdrop-blur-xl bg-white/20 rounded-3xl shadow-2xl border border-white/30 p-10 max-w-md mx-auto">
                <div className="text-center">
                  <div className="inline-flex items-center px-4 py-2 bg-emerald-50/80 rounded-full mb-6">
                    <Star className="w-4 h-4 mr-2 text-emerald-600" />
                    <span className="text-emerald-600 text-sm font-semibold">
                      Vibe Match Complete
                    </span>
                  </div>

                  <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">
                    Overall Compatibility
                  </h2>

                  {/* Enhanced Score Circle */}
                  <div className="w-40 h-40 mx-auto mb-6">
                    <CircularProgressbar
                      value={82}
                      text="82%"
                      strokeWidth={8}
                      styles={buildStyles({
                        pathColor: "url(#compatibilityGradient)",
                        textColor: "var(--text-color, #1f2937)",
                        trailColor: "rgba(156, 163, 175, 0.2)",
                        textSize: "24px",
                      })}
                    />
                    <svg style={{ height: 0, width: 0, position: "absolute" }}>
                      <defs>
                        <linearGradient
                          id="compatibilityGradient"
                          gradientTransform="rotate(90)"
                        >
                          <stop offset="0%" stopColor="#10b981" />
                          <stop offset="50%" stopColor="#14b8a6" />
                          <stop offset="100%" stopColor="#06b6d4" />
                        </linearGradient>
                      </defs>
                    </svg>
                  </div>

                  <div className="space-y-2">
                    <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full shadow-lg">
                      <Heart className="w-4 h-4 mr-2 text-white" />
                      <span className="text-white font-semibold">
                        Highly Compatible
                      </span>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Exceptional harmony with strong potential for a fulfilling
                      partnership
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Report Navigation */}
        <div className="max-w-6xl mx-auto px-6 mb-12">
          <div className="flex justify-center mb-12">
            <div className="relative backdrop-blur-xl bg-white/20 rounded-2xl p-2 shadow-xl border border-white/30">
              <div className="flex space-x-2">
                <button
                  onClick={() => setActiveTab("ai-insights")}
                  className={`relative px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center space-x-2 ${
                    activeTab === "ai-insights"
                      ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg transform scale-105"
                      : "text-gray-700 dark:text-gray-300 hover:text-purple-600 hover:bg-white/10"
                  }`}
                >
                  <BrainCircuit className="w-4 h-4" />
                  <span>AI Vibe Insights</span>
                </button>
                <button
                  onClick={() => setActiveTab("traditional")}
                  className={`relative px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center space-x-2 ${
                    activeTab === "traditional"
                      ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg transform scale-105"
                      : "text-gray-700 dark:text-gray-300 hover:text-purple-600 hover:bg-white/10"
                  }`}
                >
                  <Star className="w-4 h-4" />
                  <span>Traditional Analysis</span>
                </button>
              </div>
            </div>
          </div>

          {/* AI Insights View */}
          {activeTab === "ai-insights" && (
            <div className="space-y-8">
              {/* AI Summary Card */}
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-400/30 to-pink-400/30 rounded-3xl blur"></div>
                <div className="relative backdrop-blur-xl bg-white/20 rounded-3xl shadow-xl p-8 border border-white/30">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <BrainCircuit className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-3">
                        AI Relationship Insights
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        Your cosmic energies create a beautiful dance of
                        complementary forces. Priya's Aries fire brings passion
                        and initiative, while Rahul's Cancer water provides
                        emotional depth and nurturing stability. This fire-water
                        combination, when balanced, creates the perfect steam
                        for growth and transformation in your relationship.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Life Aspects Grid */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    name: "Emotional Sync",
                    score: 85,
                    planet: "Moon",
                    color: "from-blue-400 to-teal-300",
                    icon: Heart,
                  },
                  {
                    name: "Wealth & Prosperity",
                    score: 78,
                    planet: "Jupiter & Venus",
                    color: "from-amber-400 to-yellow-300",
                    icon: TrendingUp,
                  },
                  {
                    name: "Career & Ambition",
                    score: 90,
                    planet: "Sun & Saturn",
                    color: "from-red-500 to-orange-400",
                    icon: Star,
                  },
                  {
                    name: "Family Harmony",
                    score: 82,
                    planet: "Venus & Moon",
                    color: "from-pink-400 to-rose-400",
                    icon: Users,
                  },
                  {
                    name: "Communication",
                    score: 88,
                    planet: "Mercury",
                    color: "from-purple-400 to-indigo-400",
                    icon: MessageCircle,
                  },
                  {
                    name: "Spiritual Growth",
                    score: 91,
                    planet: "Jupiter",
                    color: "from-indigo-500 to-purple-500",
                    icon: Star,
                  },
                ].map((aspect, index) => (
                  <div key={index} className="relative group">
                    <div
                      className="absolute -inset-0.5 bg-gradient-to-r opacity-30 group-hover:opacity-50 rounded-2xl blur transition duration-300"
                      style={{
                        background: `linear-gradient(to right, ${
                          aspect.color.split(" ")[1]
                        }, ${aspect.color.split(" ")[3]})`,
                      }}
                    ></div>
                    <div className="relative backdrop-blur-xl bg-white/20 rounded-2xl p-6 border border-white/30 hover:bg-white/25 transition-all duration-300">
                      <div className="text-center">
                        <div className="w-16 h-16 mx-auto mb-4">
                          <CircularProgressbar
                            value={aspect.score}
                            text={`${aspect.score}`}
                            strokeWidth={8}
                            styles={buildStyles({
                              pathColor: `url(#gradient-${index})`,
                              textColor: "var(--text-color, #1f2937)",
                              trailColor: "rgba(156, 163, 175, 0.2)",
                              textSize: "28px",
                            })}
                          />
                          <svg
                            style={{
                              height: 0,
                              width: 0,
                              position: "absolute",
                            }}
                          >
                            <defs>
                              <linearGradient
                                id={`gradient-${index}`}
                                gradientTransform="rotate(90)"
                              >
                                <stop
                                  offset="0%"
                                  stopColor={getColorFromTailwind(
                                    aspect.color,
                                    "start"
                                  )}
                                />
                                <stop
                                  offset="100%"
                                  stopColor={getColorFromTailwind(
                                    aspect.color,
                                    "end"
                                  )}
                                />
                              </linearGradient>
                            </defs>
                          </svg>
                        </div>
                        <h4 className="font-semibold text-gray-800 dark:text-white mb-2">
                          {aspect.name}
                        </h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {aspect.planet}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* AI Chat Section */}
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400/30 to-purple-400/30 rounded-3xl blur"></div>
                <div className="relative backdrop-blur-xl bg-white/20 rounded-3xl shadow-xl p-8 border border-white/30">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                      Ask AI About Your Vibe Match
                    </h3>
                    <button
                      onClick={() => setChatOpen(!chatOpen)}
                      className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center space-x-2"
                    >
                      <MessageCircle className="w-4 h-4" />
                      <span>{chatOpen ? "Close Chat" : "Open Chat"}</span>
                    </button>
                  </div>

                  {chatOpen && (
                    <div className="space-y-4">
                      <div className="backdrop-blur-sm bg-gray-100/50 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-sm">👤</span>
                          </div>
                          <p className="text-gray-700 dark:text-gray-300">
                            "What makes our Vibe Match score so high?"
                          </p>
                        </div>
                      </div>

                      <div className="backdrop-blur-sm bg-purple-100/50 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <BrainCircuit className="w-4 h-4 text-white" />
                          </div>
                          <p className="text-gray-700 dark:text-gray-300">
                            Your high compatibility comes from perfect
                            complementary energies! Priya's Aries leadership
                            balances beautifully with Rahul's Cancer nurturing
                            nature. Your Moon signs create emotional harmony,
                            while your career ambitions align exceptionally
                            well. This creates a relationship where you both
                            grow stronger together rather than competing.
                          </p>
                        </div>
                      </div>

                      <div className="backdrop-blur-sm bg-gray-100/50 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-sm">👤</span>
                          </div>
                          <p className="text-gray-700 dark:text-gray-300">
                            "Any areas we should focus on?"
                          </p>
                        </div>
                      </div>

                      <div className="backdrop-blur-sm bg-purple-100/50 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <BrainCircuit className="w-4 h-4 text-white" />
                          </div>
                          <p className="text-gray-700 dark:text-gray-300">
                            Focus on communication timing - Priya's quick Aries
                            decisions vs Rahul's thoughtful Cancer processing.
                            Create space for both styles. Also, balance social
                            preferences: Priya's love for adventure with Rahul's
                            preference for intimate gatherings. Your differences
                            are actually your strengths when embraced!
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Traditional View */}
          {activeTab === "traditional" && (
            <div className="space-y-8">
              {/* Guna Milan Score */}
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-amber-400/30 to-orange-400/30 rounded-3xl blur"></div>
                <div className="relative backdrop-blur-xl bg-white/20 rounded-3xl shadow-xl p-8 border border-white/30">
                  <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
                    Traditional Guna Milan
                  </h3>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div className="text-center">
                      <div className="w-32 h-32 mx-auto mb-4">
                        <CircularProgressbar
                          value={(28 / 36) * 100}
                          text="28/36"
                          strokeWidth={6}
                          styles={buildStyles({
                            pathColor: "url(#traditionalGradient)",
                            textColor: "var(--text-color, #1f2937)",
                            trailColor: "rgba(156, 163, 175, 0.2)",
                            textSize: "20px",
                          })}
                        />
                        <svg
                          style={{ height: 0, width: 0, position: "absolute" }}
                        >
                          <defs>
                            <linearGradient
                              id="traditionalGradient"
                              gradientTransform="rotate(90)"
                            >
                              <stop offset="0%" stopColor="#f59e0b" />
                              <stop offset="100%" stopColor="#ea580c" />
                            </linearGradient>
                          </defs>
                        </svg>
                      </div>
                      <div className="text-amber-600 dark:text-amber-400 font-semibold text-lg">
                        Excellent Match
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mt-2">
                        Traditional Vedic Compatibility
                      </p>
                    </div>
                    <div className="space-y-3">
                      {[
                        { name: "Varna", score: 1, total: 1, status: "match" },
                        { name: "Vashya", score: 2, total: 2, status: "match" },
                        { name: "Tara", score: 3, total: 3, status: "match" },
                        { name: "Yoni", score: 4, total: 4, status: "match" },
                        {
                          name: "Graha Maitri",
                          score: 4,
                          total: 5,
                          status: "partial",
                        },
                        { name: "Gana", score: 6, total: 6, status: "match" },
                        {
                          name: "Bhakoot",
                          score: 0,
                          total: 7,
                          status: "no-match",
                        },
                        { name: "Nadi", score: 8, total: 8, status: "match" },
                      ].map((guna, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 rounded-lg backdrop-blur-sm bg-white/10 border border-white/20"
                        >
                          <span className="font-medium text-gray-700 dark:text-gray-300">
                            {guna.name}
                          </span>
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${
                              guna.status === "match"
                                ? "bg-green-100/80 text-green-800"
                                : guna.status === "partial"
                                ? "bg-yellow-100/80 text-yellow-800"
                                : "bg-red-100/80 text-red-800"
                            }`}
                          >
                            {guna.score}/{guna.total}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Family Sharing Section */}
          <div className="mt-12 relative">
            <div className="absolute -inset-1 bg-gradient-to-r from-green-400/30 to-blue-400/30 rounded-3xl blur"></div>
            <div className="relative backdrop-blur-xl bg-white/20 rounded-3xl shadow-xl p-8 border border-white/30">
              <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-6 text-center">
                Share Your Vibe Match
              </h3>
              <div className="grid md:grid-cols-3 gap-6">
                <button className="group p-6 backdrop-blur-sm bg-white/10 border-2 border-dashed border-white/30 rounded-xl hover:border-purple-400/50 hover:bg-white/20 transition-all duration-300">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-purple-100/80 rounded-full mx-auto mb-3 flex items-center justify-center group-hover:bg-purple-200/80 transition-colors">
                      <Download className="w-6 h-6 text-purple-600" />
                    </div>
                    <h4 className="font-semibold text-gray-800 dark:text-white mb-2">
                      Download PDF
                    </h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Complete report for family review
                    </p>
                  </div>
                </button>

                <button className="group p-6 backdrop-blur-sm bg-white/10 border-2 border-dashed border-white/30 rounded-xl hover:border-green-400/50 hover:bg-white/20 transition-all duration-300">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-100/80 rounded-full mx-auto mb-3 flex items-center justify-center group-hover:bg-green-200/80 transition-colors">
                      <Share2 className="w-6 h-6 text-green-600" />
                    </div>
                    <h4 className="font-semibold text-gray-800 dark:text-white mb-2">
                      Share Link
                    </h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Send to family members
                    </p>
                  </div>
                </button>

                <button className="group p-6 backdrop-blur-sm bg-white/10 border-2 border-dashed border-white/30 rounded-xl hover:border-blue-400/50 hover:bg-white/20 transition-all duration-300">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-blue-100/80 rounded-full mx-auto mb-3 flex items-center justify-center group-hover:bg-blue-200/80 transition-colors">
                      <FileText className="w-6 h-6 text-blue-600" />
                    </div>
                    <h4 className="font-semibold text-gray-800 dark:text-white mb-2">
                      Family Discussion
                    </h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Talking points for elders
                    </p>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SampleReportPage;
