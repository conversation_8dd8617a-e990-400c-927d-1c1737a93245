import React, { useState } from "react";
import {
  BrainCircuit,
  Heart,
  Star,
  Users,
  TrendingUp,
  MessageCircle,
  Download,
  Share2,
  FileText,
  Sparkles,
} from "lucide-react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

// Helper function for gradient colors
const getGradientColors = (colorType: string) => {
  const gradients = {
    emotional: { start: "#60a5fa", end: "#5eead4" },
    wealth: { start: "#fbbf24", end: "#fde047" },
    career: { start: "#ef4444", end: "#fb923c" },
    family: { start: "#f472b6", end: "#fb7185" },
    communication: { start: "#c084fc", end: "#818cf8" },
    spiritual: { start: "#6366f1", end: "#a855f7" },
  };
  return (
    gradients[colorType as keyof typeof gradients] || {
      start: "#6366f1",
      end: "#a855f7",
    }
  );
};

const SampleReportPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("ai-insights");
  const [chatOpen, setChatOpen] = useState(false);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-violet-50 via-pink-50 to-blue-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900">
      {/* Subtle Background Blobs */}
      <div className="absolute inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 w-full">
        {/* Simplified Hero Section */}
        <div className="w-full px-6 py-12 lg:py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center px-3 py-1.5 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full border border-purple-200/50 dark:border-purple-700/50 mb-6">
              <Sparkles className="w-4 h-4 mr-2 text-purple-600" />
              <span className="text-purple-700 dark:text-purple-300 text-sm font-medium">
                Compatibility Report
              </span>
            </div>

            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight text-gray-900 dark:text-white mb-4">
              Priya & Rahul
              <span className="block bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Perfect Harmony
              </span>
            </h1>

            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-xl mx-auto mb-8">
              A beautiful blend of complementary energies and shared dreams
            </p>

            {/* Simplified Couple Cards */}
            <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto mb-12">
              {/* Bride Card */}
              <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <div className="text-center">
                  <div className="relative mb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-500 rounded-full mx-auto flex items-center justify-center">
                      <span className="text-xl font-bold text-white">P</span>
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-400 rounded-full flex items-center justify-center">
                      <span className="text-sm">♈</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    Priya Sharma
                  </h3>
                  <div className="text-gray-600 dark:text-gray-300 text-sm space-y-1">
                    <p>March 15, 1995</p>
                    <p>Mumbai, India</p>
                    <div className="inline-flex items-center px-2 py-1 bg-pink-100 dark:bg-pink-900/30 rounded-full mt-2">
                      <span className="text-pink-700 dark:text-pink-300 text-xs font-medium">
                        Aries • Cancer Moon
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Groom Card */}
              <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <div className="text-center">
                  <div className="relative mb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full mx-auto flex items-center justify-center">
                      <span className="text-xl font-bold text-white">R</span>
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-400 rounded-full flex items-center justify-center">
                      <span className="text-sm">♋</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    Rahul Gupta
                  </h3>
                  <div className="text-gray-600 dark:text-gray-300 text-sm space-y-1">
                    <p>July 22, 1993</p>
                    <p>Delhi, India</p>
                    <div className="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full mt-2">
                      <span className="text-blue-700 dark:text-blue-300 text-xs font-medium">
                        Cancer • Virgo Moon
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Clean Compatibility Score */}
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 max-w-sm mx-auto mb-12 border border-white/50 dark:border-gray-700/50 shadow-lg">
              <div className="text-center">
                <div className="inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/30 rounded-full mb-4">
                  <Heart className="w-4 h-4 mr-2 text-emerald-600 dark:text-emerald-400" />
                  <span className="text-emerald-700 dark:text-emerald-300 text-sm font-medium">
                    Compatibility Score
                  </span>
                </div>

                <div className="w-32 h-32 mx-auto mb-4">
                  <CircularProgressbar
                    value={82}
                    text="82%"
                    strokeWidth={6}
                    styles={buildStyles({
                      pathColor: "url(#compatibilityGradient)",
                      textColor: "var(--text-color, #1f2937)",
                      trailColor: "rgba(156, 163, 175, 0.2)",
                      textSize: "20px",
                    })}
                  />
                  <svg style={{ height: 0, width: 0, position: "absolute" }}>
                    <defs>
                      <linearGradient
                        id="compatibilityGradient"
                        gradientTransform="rotate(90)"
                      >
                        <stop offset="0%" stopColor="#10b981" />
                        <stop offset="100%" stopColor="#06b6d4" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>

                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Highly Compatible
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Exceptional harmony with strong potential for growth
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Clean Navigation */}
        <div className="max-w-4xl mx-auto px-6 mb-8">
          <div className="flex justify-center mb-8">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-1 border border-white/50 dark:border-gray-700/50 shadow-lg">
              <div className="flex space-x-1">
                <button
                  onClick={() => setActiveTab("ai-insights")}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                    activeTab === "ai-insights"
                      ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-md"
                      : "text-gray-600 dark:text-gray-300 hover:text-purple-600 hover:bg-gray-100/50 dark:hover:bg-gray-700/50"
                  }`}
                >
                  <BrainCircuit className="w-4 h-4" />
                  <span>AI Insights</span>
                </button>
                <button
                  onClick={() => setActiveTab("traditional")}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                    activeTab === "traditional"
                      ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-md"
                      : "text-gray-600 dark:text-gray-300 hover:text-purple-600 hover:bg-gray-100/50 dark:hover:bg-gray-700/50"
                  }`}
                >
                  <Star className="w-4 h-4" />
                  <span>Traditional</span>
                </button>
              </div>
            </div>
          </div>

          {/* AI Insights View */}
          {activeTab === "ai-insights" && (
            <div className="space-y-6">
              {/* AI Summary Card */}
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <BrainCircuit className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                      AI Relationship Insights
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      Your cosmic energies create a beautiful dance of
                      complementary forces. Priya's Aries fire brings passion
                      and initiative, while Rahul's Cancer water provides
                      emotional depth and nurturing stability. This creates
                      perfect harmony for growth and transformation.
                    </p>
                  </div>
                </div>
              </div>

              {/* Life Aspects Grid */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  {
                    name: "Emotional Sync",
                    score: 85,
                    planet: "Moon",
                    colorType: "emotional",
                    icon: Heart,
                  },
                  {
                    name: "Wealth & Prosperity",
                    score: 78,
                    planet: "Jupiter & Venus",
                    colorType: "wealth",
                    icon: TrendingUp,
                  },
                  {
                    name: "Career & Ambition",
                    score: 90,
                    planet: "Sun & Saturn",
                    colorType: "career",
                    icon: Star,
                  },
                  {
                    name: "Family Harmony",
                    score: 82,
                    planet: "Venus & Moon",
                    colorType: "family",
                    icon: Users,
                  },
                  {
                    name: "Communication",
                    score: 88,
                    planet: "Mercury",
                    colorType: "communication",
                    icon: MessageCircle,
                  },
                  {
                    name: "Spiritual Growth",
                    score: 91,
                    planet: "Jupiter",
                    colorType: "spiritual",
                    icon: Sparkles,
                  },
                ].map((aspect, index) => {
                  const colors = getGradientColors(aspect.colorType);
                  return (
                    <div
                      key={index}
                      className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-3">
                          <CircularProgressbar
                            value={aspect.score}
                            text={`${aspect.score}`}
                            strokeWidth={6}
                            styles={buildStyles({
                              pathColor: `url(#gradient-${index})`,
                              textColor: "var(--text-color, #1f2937)",
                              trailColor: "rgba(156, 163, 175, 0.2)",
                              textSize: "24px",
                            })}
                          />
                          <svg
                            style={{
                              height: 0,
                              width: 0,
                              position: "absolute",
                            }}
                          >
                            <defs>
                              <linearGradient
                                id={`gradient-${index}`}
                                gradientTransform="rotate(90)"
                              >
                                <stop offset="0%" stopColor={colors.start} />
                                <stop offset="100%" stopColor={colors.end} />
                              </linearGradient>
                            </defs>
                          </svg>
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                          {aspect.name}
                        </h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {aspect.planet}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* AI Chat Section */}
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                    Ask AI About Your Match
                  </h3>
                  <button
                    onClick={() => setChatOpen(!chatOpen)}
                    className="px-3 py-1.5 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg text-sm font-medium hover:shadow-md transition-all duration-200 flex items-center space-x-2"
                  >
                    <MessageCircle className="w-4 h-4" />
                    <span>{chatOpen ? "Close" : "Chat"}</span>
                  </button>
                </div>

                {chatOpen && (
                  <div className="space-y-3">
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-xs">👤</span>
                        </div>
                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                          "What makes our compatibility score so high?"
                        </p>
                      </div>
                    </div>

                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <BrainCircuit className="w-3 h-3 text-white" />
                        </div>
                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                          Your high compatibility comes from perfect
                          complementary energies! Priya's Aries leadership
                          balances beautifully with Rahul's Cancer nurturing
                          nature, creating harmony and mutual growth.
                        </p>
                      </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-xs">👤</span>
                        </div>
                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                          "Any areas we should focus on?"
                        </p>
                      </div>
                    </div>

                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <BrainCircuit className="w-3 h-3 text-white" />
                        </div>
                        <p className="text-gray-700 dark:text-gray-300 text-sm">
                          Focus on communication timing and balancing social
                          preferences. Your differences are actually strengths
                          when embraced together!
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Traditional View */}
          {activeTab === "traditional" && (
            <div className="space-y-6">
              {/* Guna Milan Score */}
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-6 text-center">
                  Traditional Guna Milan
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="w-24 h-24 mx-auto mb-4">
                      <CircularProgressbar
                        value={(28 / 36) * 100}
                        text="28/36"
                        strokeWidth={6}
                        styles={buildStyles({
                          pathColor: "url(#traditionalGradient)",
                          textColor: "var(--text-color, #1f2937)",
                          trailColor: "rgba(156, 163, 175, 0.2)",
                          textSize: "18px",
                        })}
                      />
                      <svg
                        style={{ height: 0, width: 0, position: "absolute" }}
                      >
                        <defs>
                          <linearGradient
                            id="traditionalGradient"
                            gradientTransform="rotate(90)"
                          >
                            <stop offset="0%" stopColor="#f59e0b" />
                            <stop offset="100%" stopColor="#ea580c" />
                          </linearGradient>
                        </defs>
                      </svg>
                    </div>
                    <div className="text-amber-600 dark:text-amber-400 font-semibold">
                      Excellent Match
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                      Traditional Vedic Compatibility
                    </p>
                  </div>
                  <div className="space-y-2">
                    {[
                      { name: "Varna", score: 1, total: 1, status: "match" },
                      { name: "Vashya", score: 2, total: 2, status: "match" },
                      { name: "Tara", score: 3, total: 3, status: "match" },
                      { name: "Yoni", score: 4, total: 4, status: "match" },
                      {
                        name: "Graha Maitri",
                        score: 4,
                        total: 5,
                        status: "partial",
                      },
                      { name: "Gana", score: 6, total: 6, status: "match" },
                      {
                        name: "Bhakoot",
                        score: 0,
                        total: 7,
                        status: "no-match",
                      },
                      { name: "Nadi", score: 8, total: 8, status: "match" },
                    ].map((guna, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-700/50"
                      >
                        <span className="font-medium text-gray-700 dark:text-gray-300 text-sm">
                          {guna.name}
                        </span>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            guna.status === "match"
                              ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                              : guna.status === "partial"
                              ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300"
                              : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                          }`}
                        >
                          {guna.score}/{guna.total}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Clean Family Sharing Section */}
          <div className="mt-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-center">
              Share Your Report
            </h3>
            <div className="grid md:grid-cols-3 gap-4">
              <button className="group p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200">
                <div className="text-center">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg mx-auto mb-2 flex items-center justify-center group-hover:bg-purple-200 dark:group-hover:bg-purple-800/50 transition-colors">
                    <Download className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                    Download PDF
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-xs">
                    Complete report for family
                  </p>
                </div>
              </button>

              <button className="group p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200">
                <div className="text-center">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg mx-auto mb-2 flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-green-800/50 transition-colors">
                    <Share2 className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                    Share Link
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-xs">
                    Send to family members
                  </p>
                </div>
              </button>

              <button className="group p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200">
                <div className="text-center">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg mx-auto mb-2 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50 transition-colors">
                    <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                    Discussion Guide
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-xs">
                    Talking points for elders
                  </p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SampleReportPage;
