import React, { useState, useEffect } from "react";
import {
  BrainCircuit,
  Heart,
  Star,
  Users,
  TrendingUp,
  MessageCircle,
  Download,
  Share2,
  FileText,
  Sparkles,
  Zap,
  Crown,
  Moon,
  Sun,
  ArrowRight,
  ChevronDown,
  Play,
  Pause,
} from "lucide-react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

// World-class gradient system
const gradientSystem = {
  primary: "bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600",
  secondary: "bg-gradient-to-r from-cyan-500 to-blue-500",
  success: "bg-gradient-to-r from-emerald-500 to-teal-500",
  warning: "bg-gradient-to-r from-amber-500 to-orange-500",
  cosmic: "bg-gradient-to-br from-violet-600 via-purple-600 to-fuchsia-600",
  aurora: "bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500",
};

// Premium color palette for aspects
const aspectColors = {
  emotional: {
    from: "#3B82F6",
    to: "#06B6D4",
    bg: "from-blue-500 to-cyan-500",
  },
  wealth: {
    from: "#F59E0B",
    to: "#EAB308",
    bg: "from-amber-500 to-yellow-500",
  },
  career: { from: "#EF4444", to: "#F97316", bg: "from-red-500 to-orange-500" },
  family: { from: "#EC4899", to: "#F472B6", bg: "from-pink-500 to-rose-500" },
  communication: {
    from: "#8B5CF6",
    to: "#A855F7",
    bg: "from-violet-500 to-purple-500",
  },
  spiritual: {
    from: "#6366F1",
    to: "#8B5CF6",
    bg: "from-indigo-500 to-violet-500",
  },
};

const SampleReportPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("ai-insights");
  const [chatOpen, setChatOpen] = useState(false);


  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 relative overflow-hidden">
      {/* Premium Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated Gradient Orbs */}
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-violet-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-pink-400/20 to-rose-600/20 rounded-full blur-3xl animate-pulse animation-delay-2000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-br from-indigo-400/10 to-cyan-400/10 rounded-full blur-3xl animate-spin-slow"></div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/20 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`,
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        {/* Hero Section - World Class Design */}
        <section className="relative px-6 py-20 lg:py-32">
          <div className="max-w-7xl mx-auto">
            {/* Premium Badge */}
            <div className="flex justify-center mb-8">
              <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full shadow-2xl">
                <div className="w-2 h-2 bg-emerald-400 rounded-full mr-3 animate-pulse"></div>
                <BrainCircuit className="w-5 h-5 mr-3 text-indigo-400" />
                <span className="text-slate-700 dark:text-slate-300 font-medium">
                  AI-Powered Cosmic Analysis
                </span>
                <Crown className="w-5 h-5 ml-3 text-amber-400" />
              </div>
            </div>

            {/* Main Title */}
            <div className="text-center mb-16">
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-black leading-none mb-6">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Priya
                </span>
                <span className="text-slate-800 dark:text-white mx-4">&</span>
                <span className="bg-gradient-to-r from-pink-600 via-rose-600 to-orange-600 bg-clip-text text-transparent">
                  Rahul
                </span>
              </h1>
              <div className="relative">
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-600 dark:text-slate-300 mb-6">
                  A Cosmic Love Story Written in the Stars
                </h2>
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-transparent via-purple-500 to-transparent rounded-full"></div>
              </div>
            </div>

            {/* Premium Couple Showcase */}
            <div className="relative mb-20">
              <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
                {/* Priya's Card - Premium Design */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 to-rose-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                  <div className="relative bg-white/80 dark:bg-slate-800/80 backdrop-blur-2xl rounded-3xl p-8 border border-white/30 dark:border-slate-700/30 shadow-2xl group-hover:shadow-pink-500/25 group-hover:scale-105 transition-all duration-500">
                    <div className="text-center">
                      {/* Avatar with Zodiac Ring */}
                      <div className="relative mb-6">
                        <div className="relative w-32 h-32 mx-auto">
                          {/* Zodiac Ring */}
                          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-pink-400 to-rose-500 p-1 animate-spin-slow">
                            <div className="w-full h-full rounded-full bg-white dark:bg-slate-800"></div>
                          </div>
                          {/* Avatar */}
                          <div className="absolute inset-2 bg-gradient-to-br from-pink-400 via-rose-500 to-pink-600 rounded-full flex items-center justify-center shadow-2xl">
                            <span className="text-3xl font-black text-white">P</span>
                          </div>
                          {/* Zodiac Sign */}
                          <div className="absolute -top-2 -right-2 w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg border-4 border-white dark:border-slate-800">
                            <span className="text-xl">♈</span>
                          </div>
                        </div>
                      </div>

                      {/* Name & Details */}
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent mb-2">
                        Priya Sharma
                      </h3>
                      <div className="space-y-3 text-slate-600 dark:text-slate-300">
                        <p className="font-medium">March 15, 1995 • 10:30 AM</p>
                        <p className="text-sm">Mumbai, India</p>
                        <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/30 dark:to-rose-900/30 rounded-full border border-pink-200 dark:border-pink-700">
                          <span className="text-pink-700 dark:text-pink-300 font-semibold text-sm">
                            Aries Sun • Cancer Moon
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Rahul's Card - Premium Design */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                  <div className="relative bg-white/80 dark:bg-slate-800/80 backdrop-blur-2xl rounded-3xl p-8 border border-white/30 dark:border-slate-700/30 shadow-2xl group-hover:shadow-blue-500/25 group-hover:scale-105 transition-all duration-500">
                    <div className="text-center">
                      {/* Avatar with Zodiac Ring */}
                      <div className="relative mb-6">
                        <div className="relative w-32 h-32 mx-auto">
                          {/* Zodiac Ring */}
                          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-indigo-500 p-1 animate-spin-slow">
                            <div className="w-full h-full rounded-full bg-white dark:bg-slate-800"></div>
                          </div>
                          {/* Avatar */}
                          <div className="absolute inset-2 bg-gradient-to-br from-blue-400 via-indigo-500 to-blue-600 rounded-full flex items-center justify-center shadow-2xl">
                            <span className="text-3xl font-black text-white">R</span>
                          </div>
                          {/* Zodiac Sign */}
                          <div className="absolute -top-2 -right-2 w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg border-4 border-white dark:border-slate-800">
                            <span className="text-xl">♋</span>
                          </div>
                        </div>
                      </div>

                      {/* Name & Details */}
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                        Rahul Gupta
                      </h3>
                      <div className="space-y-3 text-slate-600 dark:text-slate-300">
                        <p className="font-medium">July 22, 1993 • 2:15 PM</p>
                        <p className="text-sm">Delhi, India</p>
                        <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full border border-blue-200 dark:border-blue-700">
                          <span className="text-blue-700 dark:text-blue-300 font-semibold text-sm">
                            Cancer Sun • Virgo Moon
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
              {/* Bride Card */}
              <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <div className="text-center">
                  <div className="relative mb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-500 rounded-full mx-auto flex items-center justify-center">
                      <span className="text-xl font-bold text-white">P</span>
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-400 rounded-full flex items-center justify-center">
                      <span className="text-sm">♈</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    Priya Sharma
                  </h3>
                  <div className="text-gray-600 dark:text-gray-300 text-sm space-y-1">
                    <p>March 15, 1995</p>
                    <p>Mumbai, India</p>
                    <div className="inline-flex items-center px-2 py-1 bg-pink-100 dark:bg-pink-900/30 rounded-full mt-2">
                      <span className="text-pink-700 dark:text-pink-300 text-xs font-medium">
                        Aries • Cancer Moon
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Groom Card */}
              <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <div className="text-center">
                  <div className="relative mb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full mx-auto flex items-center justify-center">
                      <span className="text-xl font-bold text-white">R</span>
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-400 rounded-full flex items-center justify-center">
                      <span className="text-sm">♋</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    Rahul Gupta
                  </h3>
                  <div className="text-gray-600 dark:text-gray-300 text-sm space-y-1">
                    <p>July 22, 1993</p>
                    <p>Delhi, India</p>
                    <div className="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full mt-2">
                      <span className="text-blue-700 dark:text-blue-300 text-xs font-medium">
                        Cancer • Virgo Moon
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Premium Compatibility Score - Centerpiece */}
            <div className="relative mb-24">
              <div className="max-w-2xl mx-auto">
                {/* Floating Score Card */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/30 via-teal-500/30 to-cyan-500/30 rounded-3xl blur-2xl group-hover:blur-3xl transition-all duration-700"></div>
                  <div className="relative bg-white/90 dark:bg-slate-800/90 backdrop-blur-3xl rounded-3xl p-12 border border-white/40 dark:border-slate-700/40 shadow-2xl">

                    {/* Premium Badge */}
                    <div className="flex justify-center mb-8">
                      <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full shadow-lg">
                        <Heart className="w-5 h-5 mr-3 text-white" />
                        <span className="text-white font-bold">Compatibility Analysis Complete</span>
                        <Sparkles className="w-5 h-5 ml-3 text-white" />
                      </div>
                    </div>

                    {/* Main Score Display */}
                    <div className="text-center mb-8">
                      <div className="relative w-48 h-48 mx-auto mb-8">
                        {/* Outer Glow Ring */}
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-emerald-400 to-teal-500 opacity-20 animate-pulse"></div>

                        {/* Progress Ring */}
                        <div className="relative w-full h-full">
                          <CircularProgressbar
                            value={82}
                            text=""
                            strokeWidth={4}
                            styles={buildStyles({
                              pathColor: "url(#premiumGradient)",
                              trailColor: "rgba(148, 163, 184, 0.1)",
                            })}
                          />
                          <svg style={{ height: 0, width: 0, position: "absolute" }}>
                            <defs>
                              <linearGradient id="premiumGradient" gradientTransform="rotate(90)">
                                <stop offset="0%" stopColor="#10b981" />
                                <stop offset="50%" stopColor="#14b8a6" />
                                <stop offset="100%" stopColor="#06b6d4" />
                              </linearGradient>
                            </defs>
                          </svg>
                        </div>

                        {/* Center Score */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-6xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2">
                              82%
                            </div>
                            <div className="text-sm font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">
                              Match Score
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Status Badge */}
                      <div className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl shadow-xl mb-6">
                        <Crown className="w-6 h-6 mr-3 text-white" />
                        <span className="text-white text-xl font-bold">Exceptionally Compatible</span>
                      </div>

                      <p className="text-lg text-slate-600 dark:text-slate-300 max-w-md mx-auto leading-relaxed">
                        Your cosmic energies create a harmonious symphony of love, growth, and shared destiny.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Premium Navigation */}
        <section className="relative px-6 mb-16">
          <div className="max-w-6xl mx-auto">
            <div className="flex justify-center">
              <div className="relative bg-white/80 dark:bg-slate-800/80 backdrop-blur-2xl rounded-2xl p-2 border border-white/30 dark:border-slate-700/30 shadow-2xl">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setActiveTab("ai-insights")}
                    className={`relative px-8 py-4 rounded-xl font-bold transition-all duration-300 flex items-center space-x-3 ${
                      activeTab === "ai-insights"
                        ? "bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-xl transform scale-105"
                        : "text-slate-600 dark:text-slate-300 hover:text-indigo-600 hover:bg-slate-100/50 dark:hover:bg-slate-700/50"
                    }`}
                  >
                    <BrainCircuit className="w-5 h-5" />
                    <span>AI Cosmic Insights</span>
                    {activeTab === "ai-insights" && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
                    )}
                  </button>
                  <button
                    onClick={() => setActiveTab("traditional")}
                    className={`relative px-8 py-4 rounded-xl font-bold transition-all duration-300 flex items-center space-x-3 ${
                      activeTab === "traditional"
                        ? "bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-xl transform scale-105"
                        : "text-slate-600 dark:text-slate-300 hover:text-indigo-600 hover:bg-slate-100/50 dark:hover:bg-slate-700/50"
                    }`}
                  >
                    <Star className="w-5 h-5" />
                    <span>Vedic Wisdom</span>
                    {activeTab === "traditional" && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-amber-400 rounded-full animate-pulse"></div>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

            {/* AI Insights View */}
            {activeTab === "ai-insights" && (
              <div className="space-y-12">
                {/* Premium AI Summary */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                  <div className="relative bg-white/90 dark:bg-slate-800/90 backdrop-blur-2xl rounded-3xl p-8 border border-white/40 dark:border-slate-700/40 shadow-2xl">
                    <div className="flex items-start space-x-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-xl">
                        <BrainCircuit className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4">
                          AI Cosmic Intelligence Report
                        </h3>
                        <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
                          Your cosmic energies create a magnificent symphony of complementary forces.
                          Priya's fiery Aries spirit ignites passion and bold initiative, while Rahul's
                          nurturing Cancer essence provides emotional depth and intuitive wisdom. This
                          celestial dance creates perfect harmony for mutual growth and transformation.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Premium Life Aspects Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    {
                      name: "Emotional Harmony",
                      score: 85,
                      planet: "Moon Synergy",
                      colorType: "emotional",
                      icon: Heart,
                      description: "Deep emotional connection"
                    },
                    {
                      name: "Wealth & Prosperity",
                      score: 78,
                      planet: "Jupiter & Venus",
                      colorType: "wealth",
                      icon: TrendingUp,
                      description: "Financial abundance potential"
                    },
                    {
                      name: "Career Ambition",
                      score: 90,
                      planet: "Sun & Saturn",
                      colorType: "career",
                      icon: Star,
                      description: "Professional success alignment"
                    },
                    {
                      name: "Family Harmony",
                      score: 82,
                      planet: "Venus & Moon",
                      colorType: "family",
                      icon: Users,
                      description: "Familial bliss and unity"
                    },
                    {
                      name: "Communication",
                      score: 88,
                      planet: "Mercury Magic",
                      colorType: "communication",
                      icon: MessageCircle,
                      description: "Perfect understanding"
                    },
                    {
                      name: "Spiritual Growth",
                      score: 91,
                      planet: "Jupiter Wisdom",
                      colorType: "spiritual",
                      icon: Sparkles,
                      description: "Transcendent connection"
                    },
                  ].map((aspect, index) => {
                    const colors = aspectColors[aspect.colorType as keyof typeof aspectColors];
                    return (
                      <div key={index} className="group relative">
                        <div className={`absolute inset-0 bg-gradient-to-br ${colors.bg} opacity-10 group-hover:opacity-20 rounded-2xl blur transition-all duration-300`}></div>
                        <div className="relative bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl p-6 border border-white/40 dark:border-slate-700/40 shadow-xl group-hover:shadow-2xl group-hover:scale-105 transition-all duration-300">
                          <div className="text-center">
                            <div className="w-20 h-20 mx-auto mb-4 relative">
                              <CircularProgressbar
                                value={aspect.score}
                                text=""
                                strokeWidth={6}
                                styles={buildStyles({
                                  pathColor: `url(#gradient-${index})`,
                                  trailColor: "rgba(148, 163, 184, 0.1)",
                                })}
                              />
                              <svg style={{ height: 0, width: 0, position: "absolute" }}>
                                <defs>
                                  <linearGradient id={`gradient-${index}`} gradientTransform="rotate(90)">
                                    <stop offset="0%" stopColor={colors.from} />
                                    <stop offset="100%" stopColor={colors.to} />
                                  </linearGradient>
                                </defs>
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-2xl font-black text-slate-700 dark:text-slate-200">
                                  {aspect.score}
                                </span>
                              </div>
                            </div>
                            <h4 className="text-lg font-bold text-slate-800 dark:text-white mb-2">
                              {aspect.name}
                            </h4>
                            <p className="text-sm text-slate-500 dark:text-slate-400 mb-2">
                              {aspect.planet}
                            </p>
                            <p className="text-xs text-slate-600 dark:text-slate-300">
                              {aspect.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Premium AI Chat Interface */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-blue-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent">
                        AI Cosmic Advisor
                      </h3>
                      <button
                        onClick={() => setChatOpen(!chatOpen)}
                        className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-xl font-bold hover:shadow-xl transition-all duration-300 flex items-center space-x-3"
                      >
                        <MessageCircle className="w-5 h-5" />
                        <span>{chatOpen ? "Close Chat" : "Ask AI"}</span>
                        <ArrowRight className="w-5 h-5" />
                      </button>
                    </div>

                    {chatOpen && (
                      <div className="space-y-4">
                        <div className="bg-slate-100 dark:bg-slate-700/50 rounded-2xl p-4">
                          <div className="flex items-start space-x-4">
                            <div className="w-10 h-10 bg-slate-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0">
                              <span className="text-lg">👤</span>
                            </div>
                            <p className="text-slate-700 dark:text-slate-300 font-medium">
                              "What makes our cosmic connection so powerful?"
                            </p>
                          </div>
                        </div>

                        <div className="bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-2xl p-4">
                          <div className="flex items-start space-x-4">
                            <div className="w-10 h-10 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                              <BrainCircuit className="w-5 h-5 text-white" />
                            </div>
                            <p className="text-slate-700 dark:text-slate-300">
                              Your cosmic connection transcends ordinary compatibility! Priya's fiery Aries energy
                              creates the perfect catalyst for Rahul's nurturing Cancer wisdom. Together, you form
                              a celestial symphony of passion, growth, and eternal love.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

          {/* Traditional View */}
          {activeTab === "traditional" && (
            <div className="space-y-6">
              {/* Guna Milan Score */}
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-6 text-center">
                  Traditional Guna Milan
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="w-24 h-24 mx-auto mb-4">
                      <CircularProgressbar
                        value={(28 / 36) * 100}
                        text="28/36"
                        strokeWidth={6}
                        styles={buildStyles({
                          pathColor: "url(#traditionalGradient)",
                          textColor: "var(--text-color, #1f2937)",
                          trailColor: "rgba(156, 163, 175, 0.2)",
                          textSize: "18px",
                        })}
                      />
                      <svg
                        style={{ height: 0, width: 0, position: "absolute" }}
                      >
                        <defs>
                          <linearGradient
                            id="traditionalGradient"
                            gradientTransform="rotate(90)"
                          >
                            <stop offset="0%" stopColor="#f59e0b" />
                            <stop offset="100%" stopColor="#ea580c" />
                          </linearGradient>
                        </defs>
                      </svg>
                    </div>
                    <div className="text-amber-600 dark:text-amber-400 font-semibold">
                      Excellent Match
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                      Traditional Vedic Compatibility
                    </p>
                  </div>
                  <div className="space-y-2">
                    {[
                      { name: "Varna", score: 1, total: 1, status: "match" },
                      { name: "Vashya", score: 2, total: 2, status: "match" },
                      { name: "Tara", score: 3, total: 3, status: "match" },
                      { name: "Yoni", score: 4, total: 4, status: "match" },
                      {
                        name: "Graha Maitri",
                        score: 4,
                        total: 5,
                        status: "partial",
                      },
                      { name: "Gana", score: 6, total: 6, status: "match" },
                      {
                        name: "Bhakoot",
                        score: 0,
                        total: 7,
                        status: "no-match",
                      },
                      { name: "Nadi", score: 8, total: 8, status: "match" },
                    ].map((guna, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-700/50"
                      >
                        <span className="font-medium text-gray-700 dark:text-gray-300 text-sm">
                          {guna.name}
                        </span>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            guna.status === "match"
                              ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                              : guna.status === "partial"
                              ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300"
                              : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                          }`}
                        >
                          {guna.score}/{guna.total}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Clean Family Sharing Section */}
          <div className="mt-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-center">
              Share Your Report
            </h3>
            <div className="grid md:grid-cols-3 gap-4">
              <button className="group p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200">
                <div className="text-center">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg mx-auto mb-2 flex items-center justify-center group-hover:bg-purple-200 dark:group-hover:bg-purple-800/50 transition-colors">
                    <Download className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                    Download PDF
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-xs">
                    Complete report for family
                  </p>
                </div>
              </button>

              <button className="group p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200">
                <div className="text-center">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg mx-auto mb-2 flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-green-800/50 transition-colors">
                    <Share2 className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                    Share Link
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-xs">
                    Send to family members
                  </p>
                </div>
              </button>

              <button className="group p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200">
                <div className="text-center">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg mx-auto mb-2 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50 transition-colors">
                    <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1 text-sm">
                    Discussion Guide
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-xs">
                    Talking points for elders
                  </p>
                </div>
              </button>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default SampleReportPage;
