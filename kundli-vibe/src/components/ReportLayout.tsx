import { useState, useMemo } from "react";
import { Brain<PERSON><PERSON><PERSON>it, ScrollText, Puzzle } from "lucide-react";
import BackgroundRadar<PERSON>hart from "./report-cards/BackgroundRadarChart";
import TraditionalReportCard from "./report-cards/TraditionalReportCard";
import ModernReportCard from "./report-cards/ModernReportCard";

export default function ReportLayout() {
  const [activeIndex, setActiveIndex] = useState(0);

  const cards = useMemo(
    () => [
      {
        id: "modern",
        Card: ModernReportCard,
        title: "Modern Synergy",
        Icon: Puzzle,
      },
      {
        id: "ai",
        Card: BackgroundRadarChart,
        title: "AI Vibe",
        Icon: BrainCircuit,
      },
      {
        id: "traditional",
        Card: TraditionalReportCard,
        title: "The Traditional Way",
        Icon: ScrollText,
      },
    ],
    []
  );

  const handleCardClick = (index: number) => {
    setActiveIndex(index);
  };

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-8">
      <div
        className="relative w-full max-w-5xl h-[32rem]"
        style={{ perspective: "1200px" }}
      >
        {cards.map((card, index) => {
          const offset = index - activeIndex;
          const isActive = index === activeIndex;

          const style = {
            transform: `translateX(${offset * 35}%) scale(${
              1 - Math.abs(offset) * 0.2
            }) rotateY(${offset * -15}deg)`,
            zIndex: cards.length - Math.abs(offset),
            opacity: isActive ? 1 : 0.6,
            transition: "transform 0.5s ease-out, opacity 0.5s ease-out",
          };

          return (
            <div
              key={card.id}
              className="absolute w-full h-full max-w-lg mx-auto left-0 right-0 cursor-pointer"
              style={style}
              onClick={() => handleCardClick(index)}
            >
              <div
                className={`w-full h-full rounded-3xl backdrop-blur-xl shadow-2xl border border-white/20 overflow-hidden ${
                  isActive ? "bg-black/20" : "bg-white/10"
                }`}
              >
                {isActive ? (
                  <div className="w-full h-full p-6 flex flex-col">
                    <h3 className="text-2xl font-bold text-white text-center mb-4">
                      {card.title}
                    </h3>
                    <div className="flex-grow w-full h-full">
                      <card.Card />
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full flex flex-col items-center justify-center p-6">
                    <card.Icon className="w-16 h-16 text-white/50 mb-4" />
                    <h4 className="text-xl font-bold text-white/70 text-center">
                      {card.title}
                    </h4>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
