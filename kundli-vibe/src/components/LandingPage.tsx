import ReportLayout from "./ReportLayout";

export default function LandingPage() {
  return (
    <div className="h-[calc(100vh-4rem)] w-full flex items-center justify-center px-4 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-16 items-center py-16 lg:py-24">
        {/* Left Column: Hero Content */}
        <div className="text-center lg:text-left space-y-6">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-800 dark:text-white">
            Discover Your True Connection
          </h1>
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-lg mx-auto lg:mx-0">
            Unlock a new dimension of compatibility. We blend ancient Vedic
            wisdom with modern AI to reveal your true Vibe Match.
          </p>
          <a
            href="#"
            className="inline-block px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-lg font-semibold rounded-full hover:opacity-90 transition-all duration-300 transform hover:scale-105 shadow-xl"
          >
            Get Your Free Vibe Match
          </a>
        </div>

        {/* Right Column: Report Layout */}
        <ReportLayout />
      </div>
    </div>
  );
}
