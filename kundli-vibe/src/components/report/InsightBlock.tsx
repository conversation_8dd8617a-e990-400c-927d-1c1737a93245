import { Paper, Title, Button, Text } from '@mantine/core';
import { Lock } from 'lucide-react';
import type { LucideProps } from 'lucide-react';

interface InsightBlockProps {
  headline: string;
  teaserContent: React.ReactNode;
  isLocked: boolean;
  score?: number;
  Icon?: React.FC<LucideProps>;
}

const InsightBlock: React.FC<InsightBlockProps> = ({ headline, teaserContent, isLocked, score, Icon }) => {
  return (
    <Paper withBorder radius="md" p="lg" className="relative bg-white dark:bg-slate-800">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center">
          {Icon && <Icon className="w-6 h-6 mr-3 text-violet-500" />}
          <Title order={3} className="text-slate-900 dark:text-white">{headline}</Title>
        </div>
        {score && (
          <Text size="xl" fw={700} className="text-transparent bg-clip-text bg-gradient-to-r from-violet-500 to-fuchsia-500">
            {score}
          </Text>
        )}
      </div>
      <div>{teaserContent}</div>
      {isLocked && (
        <div className="absolute inset-0 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm flex flex-col items-center justify-center rounded-md">
          <Lock className="w-12 h-12 text-slate-500 mb-4" />
          <Button
            variant="gradient"
            gradient={{ from: 'violet', to: 'fuchsia' }}
            radius="md"
          >
            Unlock Complete Report
          </Button>
        </div>
      )}
    </Paper>
  );
};

export default InsightBlock;
