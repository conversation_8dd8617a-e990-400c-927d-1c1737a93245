import { Avatar, Paper, Text } from '@mantine/core';

interface ProfileSnapshotProps {
  person: {
    name: string;
    dob: string;
    time: string;
    place: string;
    rashi: string;
    nakshatra: string;
  };
}

const ProfileSnapshot: React.FC<ProfileSnapshotProps> = ({ person }) => {
  // Using a placeholder avatar service
  const avatarUrl = `https://api.dicebear.com/7.x/initials/svg?seed=${person.name}`;

  return (
    <Paper withBorder radius="xl" p="xl" className="bg-gradient-to-br from-white/90 to-purple-50/80 dark:from-black/50 dark:to-purple-900/30 backdrop-blur-lg shadow-xl border-white/30 h-full">
      <div className="flex flex-col items-center text-center space-y-4">
        <Avatar src={avatarUrl} alt={`${person.name}'s avatar`} size="xl" radius="xl" className="ring-4 ring-purple-200 dark:ring-purple-700" />
        <div className="space-y-2">
          <Text size="xl" fw={700} className="text-slate-900 dark:text-white">{person.name}</Text>
          <Text size="sm" c="dimmed" className="font-medium">{person.dob} • {person.time}</Text>
          <Text size="sm" c="dimmed">{person.place}</Text>
          <div className="flex flex-col space-y-1 mt-3">
            <Text size="xs" c="violet" fw={600}>Rashi: {person.rashi}</Text>
            <Text size="xs" c="violet" fw={600}>Nakshatra: {person.nakshatra}</Text>
          </div>
        </div>
      </div>
    </Paper>
  );
};

export default ProfileSnapshot;
