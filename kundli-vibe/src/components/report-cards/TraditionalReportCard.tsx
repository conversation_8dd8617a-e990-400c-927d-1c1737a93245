// --- TYPE DEFINITIONS ---

interface AshtakootaPoint {
  name: string;
  score: number;
  max: number;
}

interface TraditionalTabContentData {
  title: string;
  description: string;
  gunaMilanLabel: string;
  ashtakootaPoints: AshtakootaPoint[];
}

// --- MOCK DATA ---

const traditionalData: TraditionalTabContentData = {
  title: "Ashtakoota Guna Milan",
  description:
    "The traditional 36-point system for checking core compatibility.",
  gunaMilanLabel: "Good Match",
  ashtakootaPoints: [
    { name: "<PERSON><PERSON><PERSON>", score: 1, max: 1 },
    { name: "<PERSON><PERSON><PERSON>", score: 2, max: 2 },
    { name: "<PERSON>", score: 3, max: 3 },
    { name: "<PERSON><PERSON>", score: 3, max: 4 },
    { name: "<PERSON><PERSON><PERSON>", score: 4, max: 5 },
    { name: "<PERSON><PERSON>", score: 6, max: 6 },
    { name: "<PERSON><PERSON><PERSON>", score: 2, max: 7 },
    { name: "<PERSON><PERSON>", score: 8, max: 8 },
  ],
};

// --- COMPONENT ---

const TraditionalReportCard = () => {
  const content = traditionalData;
  const totalGunaMilan = content.ashtakootaPoints.reduce(
    (acc, koota) => acc + koota.score,
    0
  );

  return (
    <div className="w-full h-full text-center flex flex-col p-2">
      <p className="text-xs text-slate-500 dark:text-white/70 mb-4 px-2">
        {content.description}
      </p>
      <div
        className="flex-grow grid grid-cols-2 gap-x-6 gap-y-3 overflow-y-auto pr-2"
        style={{ maxHeight: "220px" }}
      >
        {content.ashtakootaPoints.map((koota) => (
          <div key={koota.name} className="text-left text-xs">
            <div className="flex justify-between items-center mb-0.5">
              <span className="font-medium text-slate-800 dark:text-white/90">
                {koota.name}
              </span>
              <span className="font-semibold text-slate-900 dark:text-white">
                {koota.score}
                <span className="text-slate-500 dark:text-white/60">
                  /{koota.max}
                </span>
              </span>
            </div>
            <div className="w-full bg-slate-200 dark:bg-white/20 rounded-full h-1.5">
              <div
                className="bg-gradient-to-r from-teal-400 to-cyan-500 h-1.5 rounded-full"
                style={{ width: `${(koota.score / koota.max) * 100}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-4">
        <span className="text-3xl font-bold text-white">{totalGunaMilan}</span>
        <span className="text-sm text-white/70 ml-1">/ 36</span>
        <p className="text-xs font-semibold text-teal-300 mt-1">
          {content.gunaMilanLabel}
        </p>
      </div>
    </div>
  );
};

export default TraditionalReportCard;
