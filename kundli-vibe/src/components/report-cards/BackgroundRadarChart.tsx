import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
} from "chart.js";
import { Radar } from "react-chartjs-2";

ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip);

// --- TYPE DEFINITIONS ---

interface AspectScore {
  label: string;
  score: number;
}

interface AITabContentData {
  title: string;
  description: string;
  overallScore: number;
  scoreLabel: string;
  aspectScores: AspectScore[];
}

// --- MOCK DATA ---

const aiData: AITabContentData = {
  title: "Holistic Life Compatibility",
  description: "AI-driven analysis across 10 core life aspects for a complete view of your connection.",
  overallScore: 76,
  scoreLabel: "Strong & Supportive",
  aspectScores: [
    { label: "Emotional", score: 78 },
    { label: "Health", score: 85 },
    { label: "Finance", score: 72 },
    { label: "Career", score: 88 },
    { label: "Family", score: 68 },
    { label: "Children", score: 75 },
    { label: "Intimacy", score: 82 },
    { label: "Values", score: 90 },
    { label: "Timing", score: 65 },
    { label: "Social", score: 79 },
  ],
};

// --- COMPONENT ---

const BackgroundRadarChart = () => {
  const content = aiData;
  const data = {
    labels: content.aspectScores.map((a) => a.label),
    datasets: [
      {
        label: "Compatibility Score",
        data: content.aspectScores.map((a) => a.score),
        backgroundColor: "rgba(139, 92, 246, 0.2)",
        borderColor: "rgba(139, 92, 246, 1)",
        borderWidth: 2,
        pointBackgroundColor: "rgba(139, 92, 246, 1)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgba(139, 92, 246, 1)",
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      r: {
        angleLines: {
          color: "rgba(255, 255, 255, 0.2)",
        },
        grid: {
          color: "rgba(255, 255, 255, 0.2)",
        },
        pointLabels: {
          color: "#E5E7EB", // gray-200
          font: {
            size: 10,
          },
        },
        ticks: {
          color: "rgba(255, 255, 255, 0.7)",
          backdropColor: "transparent",
          stepSize: 25,
          max: 100,
          min: 0,
        },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
      },
    },
  };

  return (
    <div className="w-full h-full">
        <Radar data={data} options={options} />
    </div>
  );
};

export default BackgroundRadarChart;
