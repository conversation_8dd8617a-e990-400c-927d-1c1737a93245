import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

// --- TYPE DEFINITIONS ---

interface LifeAspect {
  name: string;
  score: number;
  planet: string;
  color: string;
}

interface ModernReportData {
  description: string;
  aspects: LifeAspect[];
}

// --- MOCK DATA ---

const modernData: ModernReportData = {
  description:
    "A holistic view of how your energies align across key areas of life, powered by AI.",
  aspects: [
    {
      name: "Emotional Sync",
      score: 82,
      planet: "Moon",
      color: "from-blue-400 to-teal-300",
    },
    {
      name: "Wealth & Prosperity",
      score: 75,
      planet: "Jupiter & Venus",
      color: "from-amber-400 to-yellow-300",
    },
    {
      name: "Career & Ambition",
      score: 88,
      planet: "Sun & Saturn",
      color: "from-red-500 to-orange-400",
    },
    {
      name: "Domestic Bliss",
      score: 79,
      planet: "Venus & Moon",
      color: "from-pink-400 to-rose-400",
    },
    {
      name: "Values & Beliefs",
      score: 91,
      planet: "Jupiter",
      color: "from-purple-400 to-indigo-400",
    },
  ],
};

// --- COMPONENT ---

const ModernReportCard = () => {
  const { description, aspects } = modernData;

  return (
    <div className="w-full h-full text-center flex flex-col p-2">
      <p className="text-xs text-slate-500 dark:text-white/70 mb-2 px-2">
        {description}
      </p>
      <div className="flex-grow flex flex-wrap justify-center items-center content-center gap-x-4 gap-y-2 px-2">
        {aspects.map((aspect) => (
          <Gauge key={aspect.name} aspect={aspect} />
        ))}
      </div>
    </div>
  );
};

const Gauge = ({ aspect }: { aspect: LifeAspect }) => {
  const [start, end] = aspect.color.split(" ");
  const gradientId = `gradient-${aspect.name.replace(/\s+/g, "-")}`;

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="w-20 h-20 relative">
        <svg style={{ height: 0, width: 0, position: "absolute" }}>
          <defs>
            <linearGradient id={gradientId} gradientTransform="rotate(90)">
              <stop offset="0%" stopColor={start.replace("from-", "")} />
              <stop offset="100%" stopColor={end.replace("to-", "")} />
            </linearGradient>
          </defs>
        </svg>
        <CircularProgressbar
          value={aspect.score}
          text={`${aspect.score}`}
          strokeWidth={8}
          styles={buildStyles({
            pathColor: `url(#${gradientId})`,
            textColor: "var(--text-color, #ffffff)",
            trailColor: "var(--trail-color, rgba(255, 255, 255, 0.2))",
            textSize: "28px",
          })}
        />
      </div>
      <p className="text-xs font-medium text-slate-800 dark:text-white/90 mt-2 text-center leading-tight">
        {aspect.name}
      </p>
      <p className="text-[10px] text-slate-500 dark:text-white/50">
        {aspect.planet}
      </p>
    </div>
  );
};

export default ModernReportCard;
