import Layout from './components/Layout';
import LandingPage from './components/LandingPage';
import ReportLayout from './components/ReportLayout';
import SampleReportPage from './pages/SampleReportPage';
import { Routes, Route } from 'react-router-dom';

function App() {
  return (
    <Routes>
      <Route path="/" element={<Layout><LandingPage /></Layout>} />
      <Route path="/report-carousel" element={<Layout><ReportLayout /></Layout>} />
      <Route path="/sample-report" element={<Layout><SampleReportPage /></Layout>} />
    </Routes>
  );
}

export default App;
